export class Book {
    constructor(title, author, year) {
        this._bookTitle = title;
        this._bookAuthor = author;
        this._bookYear = year;
    }

    get bookTitle() {
        return this._bookTitle;
    }
    get bookAuthor() {
        return this._bookAuthor;
    }
    get bookYear() {
        return this._bookYear;
    }

    set bookAuthor(value) {
        if (value.length < 2) {
            console.log("Author is too short.");
            return;
        }
        this._bookAuthor = value;
    }

    set bookTitle(value) {
        if (value.length < 2) {
            console.log("Title is too short.");
            return;
        }
        this._bookTitle = value;
    }

    set bookYear(value) {
        if (value <= 0) {
            console.log("Year is not valid.");
            return;
        }
        this._bookYear = value;
    }

    get printInfo () {
        return `Title: ${this._bookTitle}, Author: ${this._bookAuthor}, Year: ${this._bookYear}`;
    }

    static olderBook (bookArray){
        let bookArray = [];
        return bookArray.Math.min();
    }
}