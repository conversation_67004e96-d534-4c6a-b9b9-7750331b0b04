{"name": "aqa-advanced", "version": "1.0.0", "description": "Now I can use git", "main": "index.js", "scripts": {"prepare": "husky install", "lint": "npx eslint --fix .", "test": "jest"}, "repository": {"type": "git", "url": "git+https://github.com/juliyakomeristaya/aqa-advanced.git"}, "author": "", "license": "ISC", "bugs": {"url": "https://github.com/juliyakomeristaya/aqa-advanced/issues"}, "homepage": "https://github.com/juliyakomeristaya/aqa-advanced#readme", "dependencies": {"axios": "^1.11.0", "chalk": "^5.4.1"}, "devDependencies": {"@faker-js/faker": "^10.0.0", "eslint": "^8.54.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.29.0", "husky": "^8.0.3", "jest-html-reporters": "^3.1.7", "prettier": "3.0.3"}}