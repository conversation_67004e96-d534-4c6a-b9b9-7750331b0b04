import axios from 'axios';
import postController from './controllers/PostController.js';
import { faker } from '@faker-js/faker';

describe('Jsonplaceholder tests', () => {
	describe('Posts tests', () => {
		const randomId = Math.floor(Math.random() * 10) + 1;
		console.log(randomId);
		test('should return posts list', async () => {
			const response = await postController.getAllPosts();
			expect(response.status).toEqual(200);
			expect(response.data.length).toBe(100);
		});

		test('should return post by id', async () => {
			const response = await postController.getPostById(randomId);
			expect(response.status).toEqual(200);
			expect(response.data).toHaveProperty('id', randomId);
			expect(response.data).toHaveProperty('userId', 1);
			expect(response.data).toHaveProperty('title');
			expect(response.data).toHaveProperty('body');
		});

		test('should return comments for post id', async () => {
			const response = await postController.getCommentsForPost(randomId);
			expect(response.status).toEqual(200);
			expect(response.data.length).toBe(5);
		});

		test('should edit post by id', async () => {
			const editData = {
				title: faker.book.title(),
				body: faker.lorem.paragraph()
			};
			const responseEdit = await postController.editPostById(randomId, editData);
			expect(responseEdit.status).toEqual(200);
			expect(responseEdit.data).toHaveProperty('id', randomId);
		});

		test('should delete post by id', async () => {
			const responseEdit = await postController.deletePostById(randomId);
			expect(responseEdit.status).toEqual(200);
			expect(responseEdit.data).toEqual({});
		});
	});

	describe('Users tests', () => {
		test('should return users list', async () => {
			const response = await axios.get('https://jsonplaceholder.typicode.com/users');
			expect(response.status).toEqual(200);
			expect(response.data.length).toBe(10);
		});

		test('should return user by id', async () => {
			const response = await axios.get('https://jsonplaceholder.typicode.com/users/1');
			expect(response.status).toEqual(200);
			expect(response.data).toHaveProperty('id', 1);
			expect(response.data).toHaveProperty('name', 'Leanne Graham');
			expect(response.data).toHaveProperty('username', 'Bret');
			expect(response.data).toHaveProperty('email', '<EMAIL>');
		});

		test('should delete user by id', async () => {
			const response = await axios.delete('https://jsonplaceholder.typicode.com/users/1');
			expect(response.status).toEqual(200);
			expect(response.data).toEqual({});
		});
	});

	describe('Albums tests', () => {
		test('should return albums list', async () => {
			const response = await axios.get('https://jsonplaceholder.typicode.com/albums');
			expect(response.status).toEqual(200);
			expect(response.data.length).toBe(100);
		});

		test('should return albums by user id', async () => {
			const response = await axios.get('https://jsonplaceholder.typicode.com/albums?userId=1');
			expect(response.status).toEqual(200);
			expect(response.data.length).toBe(10);
		});
	});
});
