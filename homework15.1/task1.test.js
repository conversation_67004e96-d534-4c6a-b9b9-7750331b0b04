const booksController = require('./controllers/BooksController.js');
const authorizeController = require('./controllers/AuthorizeController.js');
const mainUser = require('./testData/users.js');
const mainBooks = require('./testData/books.js');

describe('Book store tests', () => {
	let token;
	let isbn1;
	let isbn2;
	beforeAll(async () => {
		const responseToken = await authorizeController.getToken(mainUser.userName, mainUser.password);
		token = responseToken.data.token;
		const randomBookIndex = Math.floor(Math.random() * 8);
	    isbn1 = mainBooks.books[randomBookIndex].isbn;
		const randomBookIndex2 = Math.floor(Math.random() * 8);
	    isbn2 = mainBooks.books[randomBookIndex2].isbn;
	});
	describe('Books tests', () => {
		test('should return books list', async () => {
			const response = await booksController.getAllBooks();
			expect(response.status).toEqual(200);
			expect(response.data.books).toHaveLength(8);
		});
		test('should add book to user',async () => {
			const responseAddBook = await booksController.addBookToUser(isbn1, token);
			expect(responseAddBook.status).toEqual(201);
			expect(responseAddBook.data.books[0].isbn).toBe(isbn1);
		});
		test('should delete all book from user',async () => {
			const responseAddBook = await booksController.addBookToUser(isbn2, token);
			expect(responseAddBook.status).toEqual(201);
			expect(responseAddBook.data.books[0].isbn).toBe(isbn2);
			
			const responseDeleteBook = await booksController.deleteBooksFromUser(mainUser.userId,token);
			expect(responseDeleteBook.status).toEqual(204);

			const responseCheck = await booksController.getUserInfo(mainUser.userId,token);
			expect(responseCheck.data.books).toHaveLength(0);
		});
	
		afterAll(async () => {
		const responseDeleteBook = await booksController.deleteBooksFromUser(mainUser.userId,token);
		expect(responseDeleteBook.status).toEqual(204);
	});
	});
});
