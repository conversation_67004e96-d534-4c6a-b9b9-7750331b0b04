window.jest_html_reporters_callback__({"numFailedTestSuites":1,"numFailedTests":0,"numPassedTestSuites":3,"numPassedTests":23,"numPendingTestSuites":0,"numPendingTests":0,"numRuntimeErrorTestSuites":1,"numTodoTests":0,"numTotalTestSuites":4,"numTotalTests":23,"startTime":1757867515088,"success":false,"testResults":[{"numFailingTests":0,"numPassingTests":0,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":0,"runtime":0,"slow":false,"start":0},"testFilePath":"/Users/<USER>/Documents/hillel/aqa-advanced/homework10/test.js","failureMessage":"  \u001b[1m● \u001b[22mTest suite failed to run\n\n    Your test suite must contain at least one test.\n\n      \u001b[2mat onResult (\u001b[22mnode_modules/@jest/core/build/TestScheduler.js\u001b[2m:133:18)\u001b[22m\n      \u001b[2mat \u001b[22mnode_modules/@jest/core/build/TestScheduler.js\u001b[2m:254:19\u001b[22m\n      \u001b[2mat \u001b[22mnode_modules/emittery/index.js\u001b[2m:363:13\u001b[22m\n          at Array.map (<anonymous>)\n      \u001b[2mat Emittery.emit (\u001b[22mnode_modules/emittery/index.js\u001b[2m:361:23)\u001b[22m\n","testResults":[]},{"numFailingTests":0,"numPassingTests":10,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":1757867516872,"runtime":1597,"slow":false,"start":1757867515275},"testFilePath":"/Users/<USER>/Documents/hillel/aqa-advanced/homework14/task1.test.js","failureMessage":null,"testResults":[{"ancestorTitles":["Jsonplaceholder tests","Posts tests"],"duration":173,"failureMessages":[],"fullName":"Jsonplaceholder tests Posts tests should return posts list","status":"passed","title":"should return posts list"},{"ancestorTitles":["Jsonplaceholder tests","Posts tests"],"duration":46,"failureMessages":[],"fullName":"Jsonplaceholder tests Posts tests should return post by id","status":"passed","title":"should return post by id"},{"ancestorTitles":["Jsonplaceholder tests","Posts tests"],"duration":44,"failureMessages":[],"fullName":"Jsonplaceholder tests Posts tests should return comments for post id","status":"passed","title":"should return comments for post id"},{"ancestorTitles":["Jsonplaceholder tests","Posts tests"],"duration":396,"failureMessages":[],"fullName":"Jsonplaceholder tests Posts tests should edit post by id","status":"passed","title":"should edit post by id"},{"ancestorTitles":["Jsonplaceholder tests","Posts tests"],"duration":156,"failureMessages":[],"fullName":"Jsonplaceholder tests Posts tests should delete post by id","status":"passed","title":"should delete post by id"},{"ancestorTitles":["Jsonplaceholder tests","Users tests"],"duration":42,"failureMessages":[],"fullName":"Jsonplaceholder tests Users tests should return users list","status":"passed","title":"should return users list"},{"ancestorTitles":["Jsonplaceholder tests","Users tests"],"duration":51,"failureMessages":[],"fullName":"Jsonplaceholder tests Users tests should return user by id","status":"passed","title":"should return user by id"},{"ancestorTitles":["Jsonplaceholder tests","Users tests"],"duration":156,"failureMessages":[],"fullName":"Jsonplaceholder tests Users tests should delete user by id","status":"passed","title":"should delete user by id"},{"ancestorTitles":["Jsonplaceholder tests","Albums tests"],"duration":55,"failureMessages":[],"fullName":"Jsonplaceholder tests Albums tests should return albums list","status":"passed","title":"should return albums list"},{"ancestorTitles":["Jsonplaceholder tests","Albums tests"],"duration":47,"failureMessages":[],"fullName":"Jsonplaceholder tests Albums tests should return albums by user id","status":"passed","title":"should return albums by user id"}]},{"numFailingTests":0,"numPassingTests":10,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":1757867517539,"runtime":2264,"slow":false,"start":1757867515275},"testFilePath":"/Users/<USER>/Documents/hillel/aqa-advanced/homework15/task1.test.js","failureMessage":null,"testResults":[{"ancestorTitles":["Jsonplaceholder tests","Posts tests"],"duration":163,"failureMessages":[],"fullName":"Jsonplaceholder tests Posts tests should return posts list","status":"passed","title":"should return posts list"},{"ancestorTitles":["Jsonplaceholder tests","Posts tests"],"duration":46,"failureMessages":[],"fullName":"Jsonplaceholder tests Posts tests should return post by id","status":"passed","title":"should return post by id"},{"ancestorTitles":["Jsonplaceholder tests","Posts tests"],"duration":45,"failureMessages":[],"fullName":"Jsonplaceholder tests Posts tests should return comments for post id","status":"passed","title":"should return comments for post id"},{"ancestorTitles":["Jsonplaceholder tests","Posts tests"],"duration":380,"failureMessages":[],"fullName":"Jsonplaceholder tests Posts tests should edit post by id","status":"passed","title":"should edit post by id"},{"ancestorTitles":["Jsonplaceholder tests","Posts tests"],"duration":157,"failureMessages":[],"fullName":"Jsonplaceholder tests Posts tests should delete post by id","status":"passed","title":"should delete post by id"},{"ancestorTitles":["Jsonplaceholder tests","Users tests"],"duration":44,"failureMessages":[],"fullName":"Jsonplaceholder tests Users tests should return users list","status":"passed","title":"should return users list"},{"ancestorTitles":["Jsonplaceholder tests","Users tests"],"duration":49,"failureMessages":[],"fullName":"Jsonplaceholder tests Users tests should return user by id","status":"passed","title":"should return user by id"},{"ancestorTitles":["Jsonplaceholder tests","Users tests"],"duration":155,"failureMessages":[],"fullName":"Jsonplaceholder tests Users tests should delete user by id","status":"passed","title":"should delete user by id"},{"ancestorTitles":["Jsonplaceholder tests","Albums tests"],"duration":57,"failureMessages":[],"fullName":"Jsonplaceholder tests Albums tests should return albums list","status":"passed","title":"should return albums list"},{"ancestorTitles":["Jsonplaceholder tests","Albums tests"],"duration":50,"failureMessages":[],"fullName":"Jsonplaceholder tests Albums tests should return albums by user id","status":"passed","title":"should return albums by user id"}]},{"numFailingTests":0,"numPassingTests":3,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":1757867518544,"runtime":3269,"slow":false,"start":1757867515275},"testFilePath":"/Users/<USER>/Documents/hillel/aqa-advanced/homework15.1/task1.test.js","failureMessage":null,"testResults":[{"ancestorTitles":["Book store tests","Books tests"],"duration":277,"failureMessages":[],"fullName":"Book store tests Books tests should return books list","status":"passed","title":"should return books list"},{"ancestorTitles":["Book store tests","Books tests"],"duration":591,"failureMessages":[],"fullName":"Book store tests Books tests should add book to user","status":"passed","title":"should add book to user"},{"ancestorTitles":["Book store tests","Books tests"],"duration":930,"failureMessages":[],"fullName":"Book store tests Books tests should delete all book from user","status":"passed","title":"should delete all book from user"}]}],"config":{"bail":0,"changedFilesWithAncestor":false,"ci":false,"collectCoverage":false,"collectCoverageFrom":[],"coverageDirectory":"/Users/<USER>/Documents/hillel/aqa-advanced/coverage","coverageProvider":"babel","coverageReporters":["json","text","lcov","clover"],"detectLeaks":false,"detectOpenHandles":false,"errorOnDeprecated":false,"expand":false,"findRelatedTests":false,"forceExit":false,"json":false,"lastCommit":false,"listTests":false,"logHeapUsage":false,"maxConcurrency":5,"maxWorkers":7,"noStackTrace":false,"nonFlagArgs":[],"notify":false,"notifyMode":"failure-change","onlyChanged":false,"onlyFailures":false,"openHandlesTimeout":1000,"passWithNoTests":false,"projects":[],"reporters":[["default",{}],["/Users/<USER>/Documents/hillel/aqa-advanced/node_modules/jest-html-reporters/index.js",{"publicPath":"./html-report","filename":"report.html","openReport":true}]],"rootDir":"/Users/<USER>/Documents/hillel/aqa-advanced","runTestsByPath":false,"seed":-945452508,"skipFilter":false,"snapshotFormat":{"escapeString":false,"printBasicPrototype":false},"testFailureExitCode":1,"testPathPattern":"","testSequencer":"/Users/<USER>/Documents/hillel/aqa-advanced/node_modules/@jest/test-sequencer/build/index.js","updateSnapshot":"new","useStderr":false,"watch":false,"watchAll":false,"watchman":true,"workerThreads":false},"endTime":1757867518572,"_reporterOptions":{"publicPath":"./html-report","filename":"report.html","expand":false,"pageTitle":"","hideIcon":false,"testCommand":"","openReport":true,"failureMessageOnly":0,"enableMergeData":false,"dataMergeLevel":1,"inlineSource":false,"urlForTestFiles":"","darkTheme":false,"includeConsoleLog":false,"stripSkippedTest":false},"logInfoMapping":{},"attachInfos":{}})