window.jest_html_reporters_callback__({"numFailedTestSuites":0,"numFailedTests":0,"numPassedTestSuites":1,"numPassedTests":10,"numPendingTestSuites":0,"numPendingTests":0,"numRuntimeErrorTestSuites":0,"numTodoTests":0,"numTotalTestSuites":1,"numTotalTests":10,"startTime":1757681738092,"success":false,"testResults":[{"numFailingTests":0,"numPassingTests":10,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":1757681739607,"runtime":1475,"slow":false,"start":1757681738132},"testFilePath":"/Users/<USER>/Documents/hillel/aqa-advanced/homework15/task1.test.js","failureMessage":null,"testResults":[{"ancestorTitles":["Jsonplaceholder tests","Posts tests"],"duration":173,"failureMessages":[],"fullName":"Jsonplaceholder tests Posts tests should return posts list","status":"passed","title":"should return posts list"},{"ancestorTitles":["Jsonplaceholder tests","Posts tests"],"duration":57,"failureMessages":[],"fullName":"Jsonplaceholder tests Posts tests should return post by id","status":"passed","title":"should return post by id"},{"ancestorTitles":["Jsonplaceholder tests","Posts tests"],"duration":54,"failureMessages":[],"fullName":"Jsonplaceholder tests Posts tests should return comments for post id","status":"passed","title":"should return comments for post id"},{"ancestorTitles":["Jsonplaceholder tests","Posts tests"],"duration":414,"failureMessages":[],"fullName":"Jsonplaceholder tests Posts tests should edit post by id","status":"passed","title":"should edit post by id"},{"ancestorTitles":["Jsonplaceholder tests","Posts tests"],"duration":159,"failureMessages":[],"fullName":"Jsonplaceholder tests Posts tests should delete post by id","status":"passed","title":"should delete post by id"},{"ancestorTitles":["Jsonplaceholder tests","Users tests"],"duration":58,"failureMessages":[],"fullName":"Jsonplaceholder tests Users tests should return users list","status":"passed","title":"should return users list"},{"ancestorTitles":["Jsonplaceholder tests","Users tests"],"duration":99,"failureMessages":[],"fullName":"Jsonplaceholder tests Users tests should return user by id","status":"passed","title":"should return user by id"},{"ancestorTitles":["Jsonplaceholder tests","Users tests"],"duration":158,"failureMessages":[],"fullName":"Jsonplaceholder tests Users tests should delete user by id","status":"passed","title":"should delete user by id"},{"ancestorTitles":["Jsonplaceholder tests","Albums tests"],"duration":47,"failureMessages":[],"fullName":"Jsonplaceholder tests Albums tests should return albums list","status":"passed","title":"should return albums list"},{"ancestorTitles":["Jsonplaceholder tests","Albums tests"],"duration":48,"failureMessages":[],"fullName":"Jsonplaceholder tests Albums tests should return albums by user id","status":"passed","title":"should return albums by user id"}]}],"config":{"bail":0,"changedFilesWithAncestor":false,"ci":false,"collectCoverage":false,"collectCoverageFrom":[],"coverageDirectory":"/Users/<USER>/Documents/hillel/aqa-advanced/coverage","coverageProvider":"babel","coverageReporters":["json","text","lcov","clover"],"detectLeaks":false,"detectOpenHandles":false,"errorOnDeprecated":false,"expand":false,"findRelatedTests":false,"forceExit":false,"json":false,"lastCommit":false,"listTests":false,"logHeapUsage":false,"maxConcurrency":5,"maxWorkers":7,"noStackTrace":false,"nonFlagArgs":["homework15/task1.test.js"],"notify":false,"notifyMode":"failure-change","onlyChanged":false,"onlyFailures":false,"openHandlesTimeout":1000,"passWithNoTests":false,"projects":[],"reporters":[["default",{}],["/Users/<USER>/Documents/hillel/aqa-advanced/node_modules/jest-html-reporters/index.js",{"publicPath":"./html-report","filename":"report.html","openReport":true}]],"rootDir":"/Users/<USER>/Documents/hillel/aqa-advanced","runTestsByPath":false,"seed":-820263495,"skipFilter":false,"snapshotFormat":{"escapeString":false,"printBasicPrototype":false},"testFailureExitCode":1,"testPathPattern":"homework15/task1.test.js","testSequencer":"/Users/<USER>/.npm/_npx/b8d86e6551a4f492/node_modules/@jest/test-sequencer/build/index.js","updateSnapshot":"new","useStderr":false,"verbose":true,"watch":false,"watchAll":false,"watchman":true,"workerThreads":false},"endTime":1757681739616,"_reporterOptions":{"publicPath":"./html-report","filename":"report.html","expand":false,"pageTitle":"","hideIcon":false,"testCommand":"","openReport":true,"failureMessageOnly":0,"enableMergeData":false,"dataMergeLevel":1,"inlineSource":false,"urlForTestFiles":"","darkTheme":false,"includeConsoleLog":false,"stripSkippedTest":false},"logInfoMapping":{},"attachInfos":{}})