window.jest_html_reporters_callback__({"numFailedTestSuites":1,"numFailedTests":1,"numPassedTestSuites":0,"numPassedTests":2,"numPendingTestSuites":0,"numPendingTests":0,"numRuntimeErrorTestSuites":0,"numTodoTests":0,"numTotalTestSuites":1,"numTotalTests":3,"startTime":1757862418567,"success":false,"testResults":[{"numFailingTests":1,"numPassingTests":2,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":1757862420945,"runtime":2328,"slow":false,"start":1757862418617},"testFilePath":"/Users/<USER>/Documents/hillel/aqa-advanced/homework15.1/task1.test.js","failureMessage":"\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mBook store tests › Books tests › should delete all book from user\u001b[39m\u001b[22m\n\n    TypeError: booksController.getUserInfo is not a function\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 35 |\u001b[39m \t\t\texpect(responseDeleteBook\u001b[33m.\u001b[39mstatus)\u001b[33m.\u001b[39mtoEqual(\u001b[35m204\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 36 |\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 37 |\u001b[39m \t\t\t\u001b[36mconst\u001b[39m responseCheck \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m booksController\u001b[33m.\u001b[39mgetUserInfo(mainUser\u001b[33m.\u001b[39muserId\u001b[33m,\u001b[39mtoken)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m    |\u001b[39m \t\t\t                                            \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 38 |\u001b[39m \t\t\texpect(responseCheck\u001b[33m.\u001b[39mdata\u001b[33m.\u001b[39mbooks)\u001b[33m.\u001b[39mtoHaveLength(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 39 |\u001b[39m \t\t})\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 40 |\u001b[39m \t\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.getUserInfo (\u001b[22m\u001b[2m\u001b[0m\u001b[36mhomework15.1/task1.test.js\u001b[39m\u001b[0m\u001b[2m:37:48)\u001b[22m\u001b[2m\u001b[22m\n","testResults":[{"ancestorTitles":["Book store tests","Books tests"],"duration":84,"failureMessages":[],"fullName":"Book store tests Books tests should return books list","status":"passed","title":"should return books list"},{"ancestorTitles":["Book store tests","Books tests"],"duration":258,"failureMessages":[],"fullName":"Book store tests Books tests should add book to user","status":"passed","title":"should add book to user"},{"ancestorTitles":["Book store tests","Books tests"],"duration":449,"failureMessages":["TypeError: booksController.getUserInfo is not a function\n    at Object.getUserInfo (/Users/<USER>/Documents/hillel/aqa-advanced/homework15.1/task1.test.js:37:48)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)"],"fullName":"Book store tests Books tests should delete all book from user","status":"failed","title":"should delete all book from user"}]}],"config":{"bail":0,"changedFilesWithAncestor":false,"ci":false,"collectCoverage":false,"collectCoverageFrom":[],"coverageDirectory":"/Users/<USER>/Documents/hillel/aqa-advanced/coverage","coverageProvider":"babel","coverageReporters":["json","text","lcov","clover"],"detectLeaks":false,"detectOpenHandles":false,"errorOnDeprecated":false,"expand":false,"findRelatedTests":false,"forceExit":false,"json":false,"lastCommit":false,"listTests":false,"logHeapUsage":false,"maxConcurrency":5,"maxWorkers":7,"noStackTrace":false,"nonFlagArgs":["homework15.1/task1.test.js"],"notify":false,"notifyMode":"failure-change","onlyChanged":false,"onlyFailures":false,"openHandlesTimeout":1000,"passWithNoTests":false,"projects":[],"reporters":[["default",{}],["/Users/<USER>/Documents/hillel/aqa-advanced/node_modules/jest-html-reporters/index.js",{"publicPath":"./html-report","filename":"report.html","openReport":true}]],"rootDir":"/Users/<USER>/Documents/hillel/aqa-advanced","runTestsByPath":false,"seed":-**********,"skipFilter":false,"snapshotFormat":{"escapeString":false,"printBasicPrototype":false},"testFailureExitCode":1,"testPathPattern":"homework15.1/task1.test.js","testSequencer":"/Users/<USER>/.npm/_npx/b8d86e6551a4f492/node_modules/@jest/test-sequencer/build/index.js","updateSnapshot":"new","useStderr":false,"verbose":true,"watch":false,"watchAll":false,"watchman":true,"workerThreads":false},"endTime":1757862420951,"_reporterOptions":{"publicPath":"./html-report","filename":"report.html","expand":false,"pageTitle":"","hideIcon":false,"testCommand":"","openReport":true,"failureMessageOnly":0,"enableMergeData":false,"dataMergeLevel":1,"inlineSource":false,"urlForTestFiles":"","darkTheme":false,"includeConsoleLog":false,"stripSkippedTest":false},"logInfoMapping":{},"attachInfos":{}})