window.jest_html_reporters_callback__({"numFailedTestSuites":0,"numFailedTests":0,"numPassedTestSuites":3,"numPassedTests":23,"numPendingTestSuites":0,"numPendingTests":0,"numRuntimeErrorTestSuites":0,"numTodoTests":0,"numTotalTestSuites":3,"numTotalTests":23,"startTime":1757869634776,"success":false,"testResults":[{"numFailingTests":0,"numPassingTests":10,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":1757869636182,"runtime":1261,"slow":false,"start":1757869634921},"testFilePath":"/Users/<USER>/Documents/hillel/aqa-advanced/homework14/task1.test.js","failureMessage":null,"testResults":[{"ancestorTitles":["Jsonplaceholder tests","Posts tests"],"duration":134,"failureMessages":[],"fullName":"Jsonplaceholder tests Posts tests should return posts list","status":"passed","title":"should return posts list"},{"ancestorTitles":["Jsonplaceholder tests","Posts tests"],"duration":38,"failureMessages":[],"fullName":"Jsonplaceholder tests Posts tests should return post by id","status":"passed","title":"should return post by id"},{"ancestorTitles":["Jsonplaceholder tests","Posts tests"],"duration":36,"failureMessages":[],"fullName":"Jsonplaceholder tests Posts tests should return comments for post id","status":"passed","title":"should return comments for post id"},{"ancestorTitles":["Jsonplaceholder tests","Posts tests"],"duration":147,"failureMessages":[],"fullName":"Jsonplaceholder tests Posts tests should edit post by id","status":"passed","title":"should edit post by id"},{"ancestorTitles":["Jsonplaceholder tests","Posts tests"],"duration":258,"failureMessages":[],"fullName":"Jsonplaceholder tests Posts tests should delete post by id","status":"passed","title":"should delete post by id"},{"ancestorTitles":["Jsonplaceholder tests","Users tests"],"duration":32,"failureMessages":[],"fullName":"Jsonplaceholder tests Users tests should return users list","status":"passed","title":"should return users list"},{"ancestorTitles":["Jsonplaceholder tests","Users tests"],"duration":41,"failureMessages":[],"fullName":"Jsonplaceholder tests Users tests should return user by id","status":"passed","title":"should return user by id"},{"ancestorTitles":["Jsonplaceholder tests","Users tests"],"duration":146,"failureMessages":[],"fullName":"Jsonplaceholder tests Users tests should delete user by id","status":"passed","title":"should delete user by id"},{"ancestorTitles":["Jsonplaceholder tests","Albums tests"],"duration":39,"failureMessages":[],"fullName":"Jsonplaceholder tests Albums tests should return albums list","status":"passed","title":"should return albums list"},{"ancestorTitles":["Jsonplaceholder tests","Albums tests"],"duration":43,"failureMessages":[],"fullName":"Jsonplaceholder tests Albums tests should return albums by user id","status":"passed","title":"should return albums by user id"}]},{"numFailingTests":0,"numPassingTests":10,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":1757869636773,"runtime":1852,"slow":false,"start":1757869634921},"testFilePath":"/Users/<USER>/Documents/hillel/aqa-advanced/homework15/task1.test.js","failureMessage":null,"testResults":[{"ancestorTitles":["Jsonplaceholder tests","Posts tests"],"duration":131,"failureMessages":[],"fullName":"Jsonplaceholder tests Posts tests should return posts list","status":"passed","title":"should return posts list"},{"ancestorTitles":["Jsonplaceholder tests","Posts tests"],"duration":40,"failureMessages":[],"fullName":"Jsonplaceholder tests Posts tests should return post by id","status":"passed","title":"should return post by id"},{"ancestorTitles":["Jsonplaceholder tests","Posts tests"],"duration":40,"failureMessages":[],"fullName":"Jsonplaceholder tests Posts tests should return comments for post id","status":"passed","title":"should return comments for post id"},{"ancestorTitles":["Jsonplaceholder tests","Posts tests"],"duration":150,"failureMessages":[],"fullName":"Jsonplaceholder tests Posts tests should edit post by id","status":"passed","title":"should edit post by id"},{"ancestorTitles":["Jsonplaceholder tests","Posts tests"],"duration":149,"failureMessages":[],"fullName":"Jsonplaceholder tests Posts tests should delete post by id","status":"passed","title":"should delete post by id"},{"ancestorTitles":["Jsonplaceholder tests","Users tests"],"duration":35,"failureMessages":[],"fullName":"Jsonplaceholder tests Users tests should return users list","status":"passed","title":"should return users list"},{"ancestorTitles":["Jsonplaceholder tests","Users tests"],"duration":39,"failureMessages":[],"fullName":"Jsonplaceholder tests Users tests should return user by id","status":"passed","title":"should return user by id"},{"ancestorTitles":["Jsonplaceholder tests","Users tests"],"duration":154,"failureMessages":[],"fullName":"Jsonplaceholder tests Users tests should delete user by id","status":"passed","title":"should delete user by id"},{"ancestorTitles":["Jsonplaceholder tests","Albums tests"],"duration":50,"failureMessages":[],"fullName":"Jsonplaceholder tests Albums tests should return albums list","status":"passed","title":"should return albums list"},{"ancestorTitles":["Jsonplaceholder tests","Albums tests"],"duration":41,"failureMessages":[],"fullName":"Jsonplaceholder tests Albums tests should return albums by user id","status":"passed","title":"should return albums by user id"}]},{"numFailingTests":0,"numPassingTests":3,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":1757869638477,"runtime":3556,"slow":false,"start":1757869634921},"testFilePath":"/Users/<USER>/Documents/hillel/aqa-advanced/homework15.1/task1.test.js","failureMessage":null,"testResults":[{"ancestorTitles":["Book store tests","Books tests"],"duration":508,"failureMessages":[],"fullName":"Book store tests Books tests should return books list","status":"passed","title":"should return books list"},{"ancestorTitles":["Book store tests","Books tests"],"duration":606,"failureMessages":[],"fullName":"Book store tests Books tests should add book to user","status":"passed","title":"should add book to user"},{"ancestorTitles":["Book store tests","Books tests"],"duration":1023,"failureMessages":[],"fullName":"Book store tests Books tests should delete all book from user","status":"passed","title":"should delete all book from user"}]}],"config":{"bail":0,"changedFilesWithAncestor":false,"ci":false,"collectCoverage":false,"collectCoverageFrom":[],"coverageDirectory":"/Users/<USER>/Documents/hillel/aqa-advanced/coverage","coverageProvider":"babel","coverageReporters":["json","text","lcov","clover"],"detectLeaks":false,"detectOpenHandles":false,"errorOnDeprecated":false,"expand":false,"findRelatedTests":false,"forceExit":false,"json":false,"lastCommit":false,"listTests":false,"logHeapUsage":false,"maxConcurrency":5,"maxWorkers":7,"noStackTrace":false,"nonFlagArgs":[],"notify":false,"notifyMode":"failure-change","onlyChanged":false,"onlyFailures":false,"openHandlesTimeout":1000,"passWithNoTests":false,"projects":[],"reporters":[["default",{}],["/Users/<USER>/Documents/hillel/aqa-advanced/node_modules/jest-html-reporters/index.js",{"publicPath":"./html-report","filename":"report.html","openReport":true}]],"rootDir":"/Users/<USER>/Documents/hillel/aqa-advanced","runTestsByPath":false,"seed":-**********,"skipFilter":false,"snapshotFormat":{"escapeString":false,"printBasicPrototype":false},"testFailureExitCode":1,"testPathPattern":"","testSequencer":"/Users/<USER>/Documents/hillel/aqa-advanced/node_modules/@jest/test-sequencer/build/index.js","updateSnapshot":"new","useStderr":false,"watch":false,"watchAll":false,"watchman":true,"workerThreads":false},"endTime":1757869638518,"_reporterOptions":{"publicPath":"./html-report","filename":"report.html","expand":false,"pageTitle":"","hideIcon":false,"testCommand":"","openReport":true,"failureMessageOnly":0,"enableMergeData":false,"dataMergeLevel":1,"inlineSource":false,"urlForTestFiles":"","darkTheme":false,"includeConsoleLog":false,"stripSkippedTest":false},"logInfoMapping":{},"attachInfos":{}})