window.jest_html_reporters_callback__({"numFailedTestSuites":1,"numFailedTests":0,"numPassedTestSuites":0,"numPassedTests":0,"numPendingTestSuites":0,"numPendingTests":0,"numRuntimeErrorTestSuites":1,"numTodoTests":0,"numTotalTestSuites":1,"numTotalTests":0,"startTime":1757682634121,"success":false,"testResults":[{"numFailingTests":0,"numPassingTests":0,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":0,"runtime":0,"slow":false,"start":0},"testFilePath":"/Users/<USER>/Documents/hillel/aqa-advanced/homework15/task1.test.js","failureMessage":"  \u001b[1m● \u001b[22mTest suite failed to run\n\n    \u001b[1m\u001b[31mJest encountered an unexpected token\u001b[39m\u001b[22m\n\n    Jest failed to parse a file. This happens e.g. when your code or its dependencies use non-standard JavaScript syntax, or when Jest is not configured to support such syntax.\n\n    Out of the box Jest supports Babel, which will be used to transform your files into valid JS based on your Babel configuration.\n\n    By default \"node_modules\" folder is ignored by transformers.\n\n    Here's what you can do:\n     • If you are trying to use ECMAScript Modules, see \u001b[4mhttps://jestjs.io/docs/ecmascript-modules\u001b[24m for how to enable it.\n     • If you are trying to use TypeScript, see \u001b[4mhttps://jestjs.io/docs/getting-started#using-typescript\u001b[24m\n     • To have some of your \"node_modules\" files transformed, you can specify a custom \u001b[1m\"transformIgnorePatterns\"\u001b[22m in your config.\n     • If you need a custom transformation specify a \u001b[1m\"transform\"\u001b[22m option in your config.\n     • If you simply want to mock your non-JS modules (e.g. binary assets) you can stub them out with the \u001b[1m\"moduleNameMapper\"\u001b[22m config option.\n\n    You'll find more details and examples of these config options in the docs:\n    \u001b[36mhttps://jestjs.io/docs/configuration\u001b[39m\n    For information about custom transformations, see:\n    \u001b[36mhttps://jestjs.io/docs/code-transformation\u001b[39m\n\n    \u001b[1m\u001b[31mDetails:\u001b[39m\u001b[22m\n\n    /Users/<USER>/Documents/hillel/aqa-advanced/node_modules/@faker-js/faker/dist/index.js:1\n    ({\"Object.<anonymous>\":function(module,exports,require,__dirname,__filename,jest){import{a as he,b as Ge}from\"./chunk-EX7KKKVL.js\";import{a as Ee,b as Ae}from\"./chunk-GLSQM7BA.js\";import{a as ye,b as ce}from\"./chunk-J2RPPXOW.js\";import{a as De,b as Ce}from\"./chunk-5QATRU3X.js\";import{a as Me,b as Re}from\"./chunk-WPKX44R7.js\";import{a as Se,b as Be}from\"./chunk-C3XWLMRZ.js\";import{a as He,b as Ue}from\"./chunk-T5AE4D37.js\";import{a as Fe,b as Te}from\"./chunk-JYQBVMKC.js\";import{a as Ze,b as Ie}from\"./chunk-MXEBYDF4.js\";import{a as fe}from\"./chunk-L6ZADBO7.js\";import{a as ae,b as te}from\"./chunk-LPSJBLNR.js\";import{a as oe}from\"./chunk-WFBH3POG.js\";import{a as me,b as pe}from\"./chunk-BIVSWDLN.js\";import{a as ke,b as ie}from\"./chunk-PDBS7Y2Q.js\";import{a as se,b as _e}from\"./chunk-WARIN3NB.js\";import{a as ne,b as le}from\"./chunk-E2UJNAEO.js\";import{a as xe,b as de}from\"./chunk-MTGFJMAI.js\";import{a as ue,b as Ne}from\"./chunk-JORKQWOG.js\";import{a as Pr,b as br}from\"./chunk-IPQMUZOW.js\";import{a as vr,b as gr}from\"./chunk-ZYEKVNBA.js\";import{a as Vr,b as Wr}from\"./chunk-QNO6ASHF.js\";import{a as wr}from\"./chunk-DEA2TBOY.js\";import{a as Xr,b as jr}from\"./chunk-HCGAOEEU.js\";import{a as Yr}from\"./chunk-BVTGCSSB.js\";import{a as Jr,b as qr}from\"./chunk-6ZMOYQUT.js\";import{a as Qr,b as $r}from\"./chunk-2ECI2B3U.js\";import{a as re,b as ee}from\"./chunk-4QJFSH6N.js\";import{a as Mr,b as Rr}from\"./chunk-4PRIFTAA.js\";import{a as Sr,b as Br}from\"./chunk-IOOTFDS6.js\";import{a as Hr,b as Ur}from\"./chunk-ST7ILI7W.js\";import{a as Fr,b as Tr}from\"./chunk-6IEGC3NL.js\";import{a as Zr,b as Ir}from\"./chunk-3COS7QD5.js\";import{a as hr,b as Gr}from\"./chunk-2EL733NW.js\";import{a as Lr,b as Or}from\"./chunk-A3JATQSO.js\";import{a as Kr,b as zr}from\"./chunk-Q6S2ZWNV.js\";import{a as ir}from\"./chunk-EYKDQYAH.js\";import{a as sr,b as _r}from\"./chunk-NF3TUA5I.js\";import{a as nr,b as lr}from\"./chunk-Y3IJGE4R.js\";import{a as xr,b as dr}from\"./chunk-W3W2D2NH.js\";import{a as ur,b as Nr}from\"./chunk-FSTVVTY3.js\";import{a as Er,b as Ar}from\"./chunk-IPFBKKCS.js\";import{a as kr}from\"./chunk-BKUYYLI4.js\";import{a as yr,b as cr}from\"./chunk-OKT4S3KH.js\";import{a as Dr,b as Cr}from\"./chunk-ICA3K4KO.js\";import{a as Y,b as w}from\"./chunk-ZRHYSKS4.js\";import{a as X,b as j}from\"./chunk-EJROM23G.js\";import{a as J,b as q}from\"./chunk-PTUKSM4R.js\";import{a as Q,b as $}from\"./chunk-OFZOY253.js\";import{a as er}from\"./chunk-V63ZNJJC.js\";import{a as or,b as fr}from\"./chunk-UUKUK2K5.js\";import{a as rr}from\"./chunk-7TT5MNTH.js\";import{a as ar,b as tr}from\"./chunk-3CNJTEWY.js\";import{a as mr,b as pr}from\"./chunk-C5XELZY4.js\";import{a as F,b as T}from\"./chunk-A7OWPVCS.js\";import{a as Z,b as I}from\"./chunk-HOGCZYQT.js\";import{a as h,b as G}from\"./chunk-DKZKWYIH.js\";import{a as L,b as O}from\"./chunk-USO3RQJG.js\";import{a as K,b as z}from\"./chunk-2QOESBNW.js\";import{a as P,b}from\"./chunk-IJLC2WSS.js\";import{a as v,b as g}from\"./chunk-D4U2SLSM.js\";import{a as V,b as W}from\"./chunk-VBAAP66R.js\";import{a as u,b as N}from\"./chunk-D3ZI6UDW.js\";import{a as A}from\"./chunk-MOAH2R73.js\";import{a as y,b as c}from\"./chunk-2I2EKFA6.js\";import{a as D,b as C}from\"./chunk-U22RNJ2B.js\";import{a as E}from\"./chunk-RCCYSHWF.js\";import{a as M,b as R}from\"./chunk-K5NNETXE.js\";import{a as S,b as B}from\"./chunk-XZY5TJQL.js\";import{a as r}from\"./chunk-4X5ZEQ5K.js\";import{a as U}from\"./chunk-QN2CV5WC.js\";import{a as H}from\"./chunk-QA3QK7DB.js\";import{a as e,b as a}from\"./chunk-P6LKJ3SA.js\";import{a as t,b as m}from\"./chunk-N5MEQFNW.js\";import{a as p,b as k}from\"./chunk-FYHIOWK6.js\";import{a as i}from\"./chunk-LMPJOTSL.js\";import{a as s,b as _}from\"./chunk-DBJFQXTK.js\";import{a as n,b as l}from\"./chunk-BHHONLW7.js\";import{a as x,b as d}from\"./chunk-33DZQNTB.js\";import{a as f}from\"./chunk-KKGCPNDS.js\";import{a as Le,b as Oe,c as Ke,d as ze,e as Pe,f as be,g as ve,h as ge,i as Ve,j as We,k as Ye,l as we,m as Xe,n as je,o}from\"./chunk-LTLDPYW5.js\";var uf={af_ZA:a,ar:m,az:k,base:i,bn_BD:_,cs_CZ:l,cy:d,da:N,de:A,de_AT:c,de_CH:C,dv:R,el:B,en:r,en_AU:U,en_AU_ocker:T,en_BORK:I,en_CA:G,en_GB:O,en_GH:z,en_HK:b,en_IE:g,en_IN:W,en_NG:w,en_US:j,en_ZA:q,eo:$,es:er,es_MX:fr,fa:tr,fi:pr,fr:ir,fr_BE:_r,fr_CA:lr,fr_CH:dr,fr_LU:Nr,fr_SN:Ar,he:cr,hr:Cr,hu:Rr,hy:Br,id_ID:Ur,it:Tr,ja:Ir,ka_GE:Gr,ko:Or,lv:zr,mk:br,nb_NO:gr,ne:Wr,nl:wr,nl_BE:jr,pl:qr,pt_BR:$r,pt_PT:ee,ro:fe,ro_MD:te,ru:pe,sk:ie,sr_RS_latin:_e,sv:le,ta_IN:de,th:Ne,tr:Ae,uk:ce,ur:Ce,uz_UZ_latin:Re,vi:Be,yo_NG:Ue,zh_CN:Te,zh_TW:Ie,zu_ZA:Ge};var rm={af_ZA:e,ar:t,az:p,base:o,bn_BD:s,cs_CZ:n,cy:x,da:u,de:E,de_AT:y,de_CH:D,dv:M,el:S,en:f,en_AU:H,en_AU_ocker:F,en_BORK:Z,en_CA:h,en_GB:L,en_GH:K,en_HK:P,en_IE:v,en_IN:V,en_NG:Y,en_US:X,en_ZA:J,eo:Q,es:rr,es_MX:or,fa:ar,fi:mr,fr:kr,fr_BE:sr,fr_CA:nr,fr_CH:xr,fr_LU:ur,fr_SN:Er,he:yr,hr:Dr,hu:Mr,hy:Sr,id_ID:Hr,it:Fr,ja:Zr,ka_GE:hr,ko:Lr,lv:Kr,mk:Pr,nb_NO:vr,ne:Vr,nl:Yr,nl_BE:Xr,pl:Jr,pt_BR:Qr,pt_PT:re,ro:oe,ro_MD:ae,ru:me,sk:ke,sr_RS_latin:se,sv:ne,ta_IN:xe,th:ue,tr:Ee,uk:ye,ur:De,uz_UZ_latin:Me,vi:Se,yo_NG:He,zh_CN:Fe,zh_TW:Ze,zu_ZA:he};export{Oe as Aircraft,Pe as BitcoinAddressFamily,be as BitcoinNetwork,ze as CssFunction,Ke as CssSpace,je as Faker,Le as FakerError,ve as IPv4Network,ge as Sex,Ye as SimpleFaker,e as af_ZA,uf as allFakers,rm as allLocales,t as ar,p as az,o as base,s as bn_BD,n as cs_CZ,x as cy,u as da,E as de,y as de_AT,D as de_CH,M as dv,S as el,f as en,H as en_AU,F as en_AU_ocker,Z as en_BORK,h as en_CA,L as en_GB,K as en_GH,P as en_HK,v as en_IE,V as en_IN,Y as en_NG,X as en_US,J as en_ZA,Q as eo,rr as es,or as es_MX,ar as fa,r as faker,a as fakerAF_ZA,m as fakerAR,k as fakerAZ,i as fakerBASE,_ as fakerBN_BD,l as fakerCS_CZ,d as fakerCY,N as fakerDA,A as fakerDE,c as fakerDE_AT,C as fakerDE_CH,R as fakerDV,B as fakerEL,r as fakerEN,U as fakerEN_AU,T as fakerEN_AU_ocker,I as fakerEN_BORK,G as fakerEN_CA,O as fakerEN_GB,z as fakerEN_GH,b as fakerEN_HK,g as fakerEN_IE,W as fakerEN_IN,w as fakerEN_NG,j as fakerEN_US,q as fakerEN_ZA,$ as fakerEO,er as fakerES,fr as fakerES_MX,tr as fakerFA,pr as fakerFI,ir as fakerFR,_r as fakerFR_BE,lr as fakerFR_CA,dr as fakerFR_CH,Nr as fakerFR_LU,Ar as fakerFR_SN,cr as fakerHE,Cr as fakerHR,Rr as fakerHU,Br as fakerHY,Ur as fakerID_ID,Tr as fakerIT,Ir as fakerJA,Gr as fakerKA_GE,Or as fakerKO,zr as fakerLV,br as fakerMK,gr as fakerNB_NO,Wr as fakerNE,wr as fakerNL,jr as fakerNL_BE,qr as fakerPL,$r as fakerPT_BR,ee as fakerPT_PT,fe as fakerRO,te as fakerRO_MD,pe as fakerRU,ie as fakerSK,_e as fakerSR_RS_latin,le as fakerSV,de as fakerTA_IN,Ne as fakerTH,Ae as fakerTR,ce as fakerUK,Ce as fakerUR,Re as fakerUZ_UZ_latin,Be as fakerVI,Ue as fakerYO_NG,Te as fakerZH_CN,Ie as fakerZH_TW,Ge as fakerZU_ZA,mr as fi,kr as fr,sr as fr_BE,nr as fr_CA,xr as fr_CH,ur as fr_LU,Er as fr_SN,Ve as generateMersenne32Randomizer,We as generateMersenne53Randomizer,yr as he,Dr as hr,Mr as hu,Sr as hy,Hr as id_ID,Fr as it,Zr as ja,hr as ka_GE,Lr as ko,Kr as lv,Xe as mergeLocales,Pr as mk,vr as nb_NO,Vr as ne,Yr as nl,Xr as nl_BE,Jr as pl,Qr as pt_BR,re as pt_PT,oe as ro,ae as ro_MD,me as ru,we as simpleFaker,ke as sk,se as sr_RS_latin,ne as sv,xe as ta_IN,ue as th,Ee as tr,ye as uk,De as ur,Me as uz_UZ_latin,Se as vi,He as yo_NG,Fe as zh_CN,Ze as zh_TW,he as zu_ZA};\n                                                                                      ^^^^^^\n\n    SyntaxError: Cannot use import statement outside a module\n\n    \u001b[0m \u001b[90m 1 |\u001b[39m \u001b[36mconst\u001b[39m axios \u001b[33m=\u001b[39m require(\u001b[32m'axios'\u001b[39m)\u001b[33m;\u001b[39m\n     \u001b[90m 2 |\u001b[39m \u001b[36mconst\u001b[39m postController \u001b[33m=\u001b[39m require(\u001b[32m'./controllers/PostController.js'\u001b[39m)\u001b[33m;\u001b[39m\n    \u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 3 |\u001b[39m \u001b[36mconst\u001b[39m { faker } \u001b[33m=\u001b[39m require(\u001b[32m'@faker-js/faker'\u001b[39m)\u001b[33m;\u001b[39m\n     \u001b[90m   |\u001b[39m                   \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n     \u001b[90m 4 |\u001b[39m\n     \u001b[90m 5 |\u001b[39m describe(\u001b[32m'Jsonplaceholder tests'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n     \u001b[90m 6 |\u001b[39m \tdescribe(\u001b[32m'Posts tests'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\u001b[0m\n\n      \u001b[2mat Runtime.createScriptFromCode (\u001b[22m../../../.npm/_npx/b8d86e6551a4f492/node_modules/jest-runtime/build/index.js\u001b[2m:1505:14)\u001b[22m\n      \u001b[2mat Object.require (\u001b[22m\u001b[0m\u001b[36mhomework15/task1.test.js\u001b[39m\u001b[0m\u001b[2m:3:19)\u001b[22m\n","testResults":[]}],"config":{"bail":0,"changedFilesWithAncestor":false,"ci":false,"collectCoverage":false,"collectCoverageFrom":[],"coverageDirectory":"/Users/<USER>/Documents/hillel/aqa-advanced/coverage","coverageProvider":"babel","coverageReporters":["json","text","lcov","clover"],"detectLeaks":false,"detectOpenHandles":false,"errorOnDeprecated":false,"expand":false,"findRelatedTests":false,"forceExit":false,"json":false,"lastCommit":false,"listTests":false,"logHeapUsage":false,"maxConcurrency":5,"maxWorkers":7,"noStackTrace":false,"nonFlagArgs":["homework15/task1.test.js"],"notify":false,"notifyMode":"failure-change","onlyChanged":false,"onlyFailures":false,"openHandlesTimeout":1000,"passWithNoTests":false,"projects":[],"reporters":[["default",{}],["/Users/<USER>/Documents/hillel/aqa-advanced/node_modules/jest-html-reporters/index.js",{"publicPath":"./html-report","filename":"report.html","openReport":true}]],"rootDir":"/Users/<USER>/Documents/hillel/aqa-advanced","runTestsByPath":false,"seed":37276762,"skipFilter":false,"snapshotFormat":{"escapeString":false,"printBasicPrototype":false},"testFailureExitCode":1,"testPathPattern":"homework15/task1.test.js","testSequencer":"/Users/<USER>/.npm/_npx/b8d86e6551a4f492/node_modules/@jest/test-sequencer/build/index.js","updateSnapshot":"new","useStderr":false,"verbose":true,"watch":false,"watchAll":false,"watchman":true,"workerThreads":false},"endTime":1757682634384,"_reporterOptions":{"publicPath":"./html-report","filename":"report.html","expand":false,"pageTitle":"","hideIcon":false,"testCommand":"","openReport":true,"failureMessageOnly":0,"enableMergeData":false,"dataMergeLevel":1,"inlineSource":false,"urlForTestFiles":"","darkTheme":false,"includeConsoleLog":false,"stripSkippedTest":false},"logInfoMapping":{},"attachInfos":{}})