window.jest_html_reporters_callback__({"numFailedTestSuites":0,"numFailedTests":0,"numPassedTestSuites":1,"numPassedTests":3,"numPendingTestSuites":0,"numPendingTests":0,"numRuntimeErrorTestSuites":0,"numTodoTests":0,"numTotalTestSuites":1,"numTotalTests":3,"startTime":1757867058663,"success":false,"testResults":[{"numFailingTests":0,"numPassingTests":3,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":1757867062193,"runtime":3484,"slow":false,"start":1757867058709},"testFilePath":"/Users/<USER>/Documents/hillel/aqa-advanced/homework15.1/task1.test.js","failureMessage":null,"testResults":[{"ancestorTitles":["Book store tests","Books tests"],"duration":289,"failureMessages":[],"fullName":"Book store tests Books tests should return books list","status":"passed","title":"should return books list"},{"ancestorTitles":["Book store tests","Books tests"],"duration":868,"failureMessages":[],"fullName":"Book store tests Books tests should add book to user","status":"passed","title":"should add book to user"},{"ancestorTitles":["Book store tests","Books tests"],"duration":957,"failureMessages":[],"fullName":"Book store tests Books tests should delete all book from user","status":"passed","title":"should delete all book from user"}]}],"config":{"bail":0,"changedFilesWithAncestor":false,"ci":false,"collectCoverage":false,"collectCoverageFrom":[],"coverageDirectory":"/Users/<USER>/Documents/hillel/aqa-advanced/coverage","coverageProvider":"babel","coverageReporters":["json","text","lcov","clover"],"detectLeaks":false,"detectOpenHandles":false,"errorOnDeprecated":false,"expand":false,"findRelatedTests":false,"forceExit":false,"json":false,"lastCommit":false,"listTests":false,"logHeapUsage":false,"maxConcurrency":5,"maxWorkers":7,"noStackTrace":false,"nonFlagArgs":["homework15.1/task1.test.js"],"notify":false,"notifyMode":"failure-change","onlyChanged":false,"onlyFailures":false,"openHandlesTimeout":1000,"passWithNoTests":false,"projects":[],"reporters":[["default",{}],["/Users/<USER>/Documents/hillel/aqa-advanced/node_modules/jest-html-reporters/index.js",{"publicPath":"./html-report","filename":"report.html","openReport":true}]],"rootDir":"/Users/<USER>/Documents/hillel/aqa-advanced","runTestsByPath":false,"seed":-**********,"skipFilter":false,"snapshotFormat":{"escapeString":false,"printBasicPrototype":false},"testFailureExitCode":1,"testPathPattern":"homework15.1/task1.test.js","testSequencer":"/Users/<USER>/.npm/_npx/b8d86e6551a4f492/node_modules/@jest/test-sequencer/build/index.js","updateSnapshot":"new","useStderr":false,"verbose":true,"watch":false,"watchAll":false,"watchman":true,"workerThreads":false},"endTime":1757867062205,"_reporterOptions":{"publicPath":"./html-report","filename":"report.html","expand":false,"pageTitle":"","hideIcon":false,"testCommand":"","openReport":true,"failureMessageOnly":0,"enableMergeData":false,"dataMergeLevel":1,"inlineSource":false,"urlForTestFiles":"","darkTheme":false,"includeConsoleLog":false,"stripSkippedTest":false},"logInfoMapping":{},"attachInfos":{}})